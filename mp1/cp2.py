from user import User
from utils import distance_km


def cp2_1_simple_inference(dictUsers):
    dictUsersInferred = dict()  # dict to return, store inferred results here
    # you should keep everything in dictUsers as is / read-only

    for user_id, user in dictUsers.items():
        # Create a copy of the user
        inferred_user = User(user)

        # Algorithm 1: Infer home location of user u
        if user.latlon_valid() and user.home_shared:
            # Home location is shared, no need to infer it
            # Keep the original location
            pass
        else:
            # Find friends with shared home locations
            friends_with_shared_homes = []
            for friend_id in user.friends:
                if friend_id in dictUsers:
                    friend = dictUsers[friend_id]
                    if friend.latlon_valid() and friend.home_shared:
                        friends_with_shared_homes.append(friend)

            # Calculate geographic center of homes (centroid)
            if friends_with_shared_homes:
                total_lat = sum(friend.home_lat for friend in friends_with_shared_homes)
                total_lon = sum(friend.home_lon for friend in friends_with_shared_homes)
                num_friends = len(friends_with_shared_homes)

                # Set inferred location as centroid
                inferred_user.home_lat = total_lat / num_friends
                inferred_user.home_lon = total_lon / num_friends
            # If no friends with shared locations, keep original location (which may be invalid)

        dictUsersInferred[user_id] = inferred_user

    return dictUsersInferred


def cp2_2_improved_inference(dictUsers):
    dictUsersInferred = dict()
    # TODO
    return dictUsersInferred


def cp2_calc_accuracy(truth_dict, inferred_dict):
    # distance_km(a,b): return distance between a and be in km
    # recommended standard: is accuate if distance to ground truth < 25km
    if len(truth_dict) != len(inferred_dict) or len(truth_dict) == 0:
        return 0.0
    sum = 0
    for i in truth_dict:
        if truth_dict[i].home_shared:
            sum += 1
        elif truth_dict[i].latlon_valid() and inferred_dict[i].latlon_valid():
            if (
                distance_km(
                    truth_dict[i].home_lat,
                    truth_dict[i].home_lon,
                    inferred_dict[i].home_lat,
                    inferred_dict[i].home_lon,
                )
                < 25.0
            ):
                sum += 1
    return sum * 1.0 / len(truth_dict)
